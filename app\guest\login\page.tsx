"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';

export default function GuestLogin() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log('Starting guest login process for:', email);

      // Find customer by email
      const { data: customerData, error: customerError } = await supabase
        .from('customers')
        .select('*')
        .eq('email', email)
        .maybeSingle();

      if (customerError) {
        console.error('Customer lookup error:', customerError);
        throw new Error('Failed to find account. Please try again.');
      }

      if (!customerData) {
        throw new Error('No account found with this email. Please sign up first.');
      }

      console.log('Customer found:', customerData.id);

      // Store customer info in localStorage for simple auth
      localStorage.setItem('customer', JSON.stringify(customerData));

      toast({
        title: "Welcome Back!",
        description: `Hello ${customerData.full_name || 'there'}! You're now signed in.`,
      });

      router.push('/guest');
    } catch (error: any) {
      console.error('Guest login process failed:', error);
      toast({
        title: "Login Error",
        description: error.message || 'An unexpected error occurred',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-muted">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Welcome Back</CardTitle>
          <CardDescription>
            Sign in to access your loyalty account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Signing in..." : "Sign In"}
            </Button>
            <div className="text-center text-sm text-muted-foreground">
              Don't have an account?{' '}
              <Link href="/guest/signup" className="text-primary hover:underline">
                Join now
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
