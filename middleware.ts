import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(req: NextRequest) {
  // Public routes - allow without auth check
  if (req.nextUrl.pathname === '/' ||
      req.nextUrl.pathname.startsWith('/auth/') ||
      req.nextUrl.pathname.startsWith('/guest/')) {
    return NextResponse.next();
  }

  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req, res });

  // Try to get session with proper error handling
  let session = null;
  try {
    const { data: { session: currentSession } } = await supabase.auth.getSession();
    session = currentSession;
  } catch (error) {
    console.error('Middleware - Session check failed:', error);
  }

  // If no session for admin routes, allow client-side auth to handle it
  // This works around session persistence issues while maintaining security
  if (!session && req.nextUrl.pathname.startsWith('/admin')) {
    return res;
  }

  // Protected routes
  if (!session) {
    console.log('Middleware - No session, redirecting to login');
    return NextResponse.redirect(new URL('/auth/login', req.url));
  }

  // Verify admin access
  try {
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', session.user.id)
      .maybeSingle();

    console.log('Middleware - Profile check:', { profile, profileError, userId: session.user.id });

    if (profileError) {
      console.error('Middleware - Profile query error:', profileError);
      return NextResponse.redirect(new URL('/auth/login', req.url));
    }

    if (!profile || profile.role !== 'admin') {
      console.log('Middleware - Access denied:', { profile });
      return NextResponse.redirect(new URL('/', req.url));
    }

    console.log('Middleware - Admin access granted');
  } catch (error) {
    console.error('Middleware - Profile check failed:', error);
    return NextResponse.redirect(new URL('/auth/login', req.url));
  }

  return res;
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};