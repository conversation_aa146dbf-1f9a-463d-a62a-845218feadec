import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req, res });
  const { data: { session } } = await supabase.auth.getSession();

  // Public routes
  if (req.nextUrl.pathname === '/' || 
      req.nextUrl.pathname.startsWith('/auth/')) {
    return res;
  }

  // Protected routes
  if (!session) {
    return NextResponse.redirect(new URL('/auth/login', req.url));
  }

  // Verify admin access
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', session.user.id)
    .maybeSingle();

  console.log('Middleware - Profile check:', { profile, profileError, userId: session.user.id });

  if (profileError) {
    console.error('Middleware - Profile query error:', profileError);
    return NextResponse.redirect(new URL('/auth/login', req.url));
  }

  if (!profile || profile.role !== 'admin') {
    console.log('Middleware - Access denied:', { profile });
    return NextResponse.redirect(new URL('/', req.url));
  }

  console.log('Middleware - Admin access granted');

  return res;
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};