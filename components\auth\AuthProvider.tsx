"use client";

import { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import { User } from '@supabase/supabase-js';

interface AuthContextType {
  user: User | null;
  isAdmin: boolean;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  isAdmin: false,
  loading: true,
});

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('AuthProvider - Auth state change:', { event, user: !!session?.user });
        setUser(session?.user ?? null);
        let isAdminUser = false;
        if (session?.user) {
          console.log('AuthProvider - Fetching profile for user:', session.user.id);
          const { data, error } = await supabase
            .from('profiles')
            .select('role')
            .eq('id', session.user.id)
            .maybeSingle();
          console.log('AuthProvider - Profile result:', { data, error });
          isAdminUser = data?.role === 'admin';
          setIsAdmin(isAdminUser);
        } else {
          setIsAdmin(false);
        }
        setLoading(false);
        console.log('AuthProvider - Final state:', { user: !!session?.user, isAdmin: isAdminUser });
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return (
    <AuthContext.Provider value={{ user, isAdmin, loading }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => useContext(AuthContext);