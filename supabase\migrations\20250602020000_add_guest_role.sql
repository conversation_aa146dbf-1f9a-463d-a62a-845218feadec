-- Add guest role to the profiles table and update the user creation trigger

-- First, update the profiles table to allow 'guest' role
ALTER TABLE public.profiles 
DROP CONSTRAINT IF EXISTS profiles_role_check;

ALTER TABLE public.profiles 
ADD CONSTRAINT profiles_role_check 
CHECK (role IN ('admin', 'staff', 'guest'));

-- Update the trigger function to handle role assignment based on metadata
CREATE OR REPLACE FUNCTION public.handle_new_user()
R<PERSON><PERSON>NS trigger LANGUAGE plpgsql SECURITY DEFINER SET search_path = public AS $$
DECLARE
  merchant_id_from_metadata uuid;
  role_from_metadata text;
BEGIN
  -- Try to get merchant_id and role from user metadata
  merchant_id_from_metadata := (NEW.raw_user_meta_data->>'merchant_id')::uuid;
  role_from_metadata := NEW.raw_user_meta_data->>'role';
  
  -- Default to 'admin' if no role specified (for backward compatibility)
  IF role_from_metadata IS NULL THEN
    role_from_metadata := 'admin';
  END IF;
  
  -- Validate role
  IF role_from_metadata NOT IN ('admin', 'staff', 'guest') THEN
    role_from_metadata := 'admin';
  END IF;
  
  -- Insert profile with appropriate role and merchant_id
  INSERT INTO public.profiles (id, role, merchant_id)
  VALUES (NEW.id, role_from_metadata, merchant_id_from_metadata);
  
  RETURN NEW;
END; $$;

-- Ensure the trigger is properly set up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
