import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON>, Star } from "lucide-react";

export default function GuestDashboard() {
  return (
    <div>
      <div className="mb-8 text-center">
        <h2 className="text-3xl font-bold mb-4">Welcome to Our Loyalty Program</h2>
        <p className="text-muted-foreground">
          Earn points and redeem exciting rewards
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="text-center">
            <Star className="w-12 h-12 mx-auto mb-2 text-primary" />
            <CardTitle>Your Points</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-4xl font-bold">0</p>
            <p className="text-muted-foreground">Points Available</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <Gift className="w-12 h-12 mx-auto mb-2 text-primary" />
            <CardTitle>Available Rewards</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-muted-foreground">
              No rewards available yet. Check back soon!
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}