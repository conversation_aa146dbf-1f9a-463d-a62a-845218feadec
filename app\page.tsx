import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON>, Star } from "lucide-react";
import Link from "next/link";

export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-background to-muted">
      <div className="container mx-auto px-4 py-16">
        <div className="mb-12 text-center">
          <h1 className="text-4xl font-bold mb-4">Welcome to Our Loyalty Program</h1>
          <p className="text-xl text-muted-foreground">
            Join our rewards program and start earning points today
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <Link href="/guest">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
              <CardHeader className="text-center">
                <Star className="w-12 h-12 mx-auto mb-2 text-primary" />
                <CardTitle>Customer Portal</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="mb-4 text-muted-foreground">
                  Check your points balance and redeem rewards
                </p>
                <Button>Access Portal</Button>
              </CardContent>
            </Card>
          </Link>

          <Link href="/auth/login">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
              <CardHeader className="text-center">
                <Gift className="w-12 h-12 mx-auto mb-2 text-primary" />
                <CardTitle>Business Login</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="mb-4 text-muted-foreground">
                  Manage your loyalty program and customer rewards
                </p>
                <Button>Admin Login</Button>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    </main>
  );
}