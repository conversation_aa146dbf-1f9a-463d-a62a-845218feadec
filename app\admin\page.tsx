"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Plus, Users, Gift, Trophy } from "lucide-react"
import Link from "next/link";

export default function AdminDashboard() {
  return (
    <div>
      <div className="mb-8">
        <h2 className="text-3xl font-bold mb-4">Loyalty Programs Management</h2>
        <p className="text-muted-foreground">
          Manage your customer loyalty programs and rewards
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Link href="/admin/programs/create">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="text-center">
              <Plus className="w-12 h-12 mx-auto mb-2 text-primary" />
              <CardTitle>Create New Program</CardTitle>
            </CardHeader>
            <CardContent className="text-center text-muted-foreground">
              Set up a new loyalty program with custom rewards and rules
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/customers">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="text-center">
              <Users className="w-12 h-12 mx-auto mb-2 text-primary" />
              <CardTitle>Manage Customers</CardTitle>
            </CardHeader>
            <CardContent className="text-center text-muted-foreground">
              View and manage customer profiles and points
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/rewards">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="text-center">
              <Gift className="w-12 h-12 mx-auto mb-2 text-primary" />
              <CardTitle>Rewards Catalog</CardTitle>
            </CardHeader>
            <CardContent className="text-center text-muted-foreground">
              Create and manage available rewards
            </CardContent>
          </Card>
        </Link>
      </div>

      <div className="mt-12">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="w-6 h-6 text-primary" />
              Program Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-muted rounded-lg text-center">
                <div className="text-2xl font-bold">0</div>
                <div className="text-sm text-muted-foreground">Active Programs</div>
              </div>
              <div className="p-4 bg-muted rounded-lg text-center">
                <div className="text-2xl font-bold">0</div>
                <div className="text-sm text-muted-foreground">Total Customers</div>
              </div>
              <div className="p-4 bg-muted rounded-lg text-center">
                <div className="text-2xl font-bold">0</div>
                <div className="text-sm text-muted-foreground">Rewards Claimed</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}