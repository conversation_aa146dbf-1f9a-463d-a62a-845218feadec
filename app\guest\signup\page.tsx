"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';

export default function GuestSignUp() {
  const [email, setEmail] = useState('');
  const [fullName, setFullName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const router = useRouter();
  const { toast } = useToast();

  const testDatabaseConnection = async () => {
    try {
      console.log('Testing database connection...');

      // Test basic connection
      const { data: testData, error: testError } = await supabase
        .from('merchants')
        .select('count')
        .limit(1);

      console.log('Basic connection test:', { testData, testError });

      // Test merchant query
      const { data: merchants, error: merchantError } = await supabase
        .from('merchants')
        .select('*');

      console.log('All merchants:', { merchants, merchantError });

      toast({
        title: "Database Test",
        description: `Found ${merchants?.length || 0} merchants. Check console for details.`,
      });
    } catch (error) {
      console.error('Database test failed:', error);
      toast({
        title: "Database Test Failed",
        description: `Error: ${error}`,
        variant: "destructive",
      });
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log('Starting guest signup process for:', email);
      setLoadingMessage('Creating your account...');

      console.log('Step 1: Looking for merchant...');
      // First, try to find an existing merchant
      const { data: existingMerchants, error: lookupError } = await supabase
        .from('merchants')
        .select('id, name')
        .limit(1);

      console.log('Existing merchants:', { existingMerchants, lookupError });

      let merchantId;

      if (lookupError) {
        console.error('Merchant lookup failed:', lookupError);
        throw new Error('Database error. Please try again.');
      }

      if (existingMerchants && existingMerchants.length > 0) {
        // Use existing merchant
        merchantId = existingMerchants[0].id;
        console.log('Step 2: Using existing merchant:', merchantId);
      } else {
        // No merchants exist - this means no admin has signed up yet
        console.log('Step 2: No merchants found - admin account required first');
        throw new Error('No business account found. Please ask the business owner to set up their admin account first at /auth/signup');
      }

      console.log('Step 3: Creating guest user account...');
      setLoadingMessage('Creating your guest account...');

      // Create Supabase auth user with guest role metadata
      const { data: authData, error: signUpError } = await supabase.auth.signUp({
        email,
        password: `guest_${Date.now()}`, // Generate a random password for guest
        options: {
          data: {
            role: 'guest',
            merchant_id: merchantId,
            full_name: fullName
          }
        }
      });

      if (signUpError) {
        console.error('Guest auth signup error:', signUpError);
        throw new Error(`Failed to create guest account: ${signUpError.message}`);
      }

      if (!authData.user) {
        throw new Error('Failed to create guest user');
      }

      console.log('Step 4: Guest user created successfully:', authData.user.id);

      // Wait for the trigger to create the profile
      console.log('Waiting for profile to be created...');
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log('Step 5: Creating customer record...');
      // Create customer record linked to the auth user
      const { data: customerData, error: customerError } = await supabase
        .from('customers')
        .insert([{
          id: authData.user.id, // Use the same ID as the auth user
          email,
          full_name: fullName,
          merchant_id: merchantId
        }])
        .select()
        .single();

      if (customerError) {
        console.error('Customer creation error:', customerError);
        if (customerError.code === '23505') { // Unique constraint violation
          throw new Error('An account with this email already exists.');
        }
        throw new Error(`Failed to create customer record: ${customerError.message || 'Unknown error'}`);
      }

      console.log('Step 6: Customer record created successfully:', customerData.id);

      toast({
        title: "Account Created!",
        description: "Welcome to our loyalty program! You can now start earning points.",
      });

      // Redirect to guest login to sign in with the new account
      router.push('/guest/login');
    } catch (error: any) {
      console.error('Guest signup process failed:', error);
      toast({
        title: "Signup Error",
        description: error.message || 'An unexpected error occurred',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setLoadingMessage('');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-muted">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Join Our Loyalty Program</CardTitle>
          <CardDescription>
            Sign up to start earning points and redeeming rewards
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSignUp} className="space-y-4">
            <div className="space-y-2">
              <Input
                type="text"
                placeholder="Full Name"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (loadingMessage || "Creating Account...") : "Join Loyalty Program"}
            </Button>
            <Button type="button" variant="outline" className="w-full" onClick={testDatabaseConnection}>
              Test Database Connection
            </Button>
            <div className="text-center text-sm text-muted-foreground">
              Already have an account?{' '}
              <Link href="/guest/login" className="text-primary hover:underline">
                Sign in
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
