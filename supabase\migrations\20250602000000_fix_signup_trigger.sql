-- Improve the user creation trigger to handle merchant_id from metadata
create or replace function public.handle_new_user()
returns trigger language plpgsql security definer set search_path = public as $$
declare
  merchant_id_from_metadata uuid;
begin
  -- Try to get merchant_id from user metadata
  merchant_id_from_metadata := (new.raw_user_meta_data->>'merchant_id')::uuid;
  
  -- Insert profile with merchant_id if available
  insert into public.profiles (id, role, merchant_id)
  values (new.id, 'admin', merchant_id_from_metadata);
  
  return new;
end; $$;

-- Ensure the trigger is properly set up
drop trigger if exists on_auth_user_created on auth.users;
create trigger on_auth_user_created
after insert on auth.users
for each row execute procedure public.handle_new_user();
