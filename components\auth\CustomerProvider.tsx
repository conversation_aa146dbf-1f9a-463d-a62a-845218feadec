"use client";

import { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';

interface Customer {
  id: string;
  email: string;
  full_name: string;
  merchant_id: string;
  joined_at: string;
}

interface CustomerContextType {
  customer: Customer | null;
  isCustomer: boolean;
  loading: boolean;
  signOut: () => void;
  refreshCustomer: () => Promise<void>;
}

const CustomerContext = createContext<CustomerContextType>({
  customer: null,
  isCustomer: false,
  loading: true,
  signOut: () => {},
  refreshCustomer: async () => {},
});

export function CustomerProvider({ children }: { children: React.ReactNode }) {
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);

  const refreshCustomer = async () => {
    try {
      console.log('CustomerProvider - Refreshing customer data...');
      const storedCustomer = localStorage.getItem('customer');
      console.log('CustomerProvider - Stored customer:', !!storedCustomer);

      if (storedCustomer) {
        const customerData = JSON.parse(storedCustomer);
        console.log('CustomerProvider - Parsed customer data:', customerData.id);

        // Check if this is a mock customer (development mode)
        if (customerData.id && customerData.id.startsWith('customer_')) {
          console.log('CustomerProvider - Mock customer detected, skipping database verification');
          setCustomer(customerData);
        } else {
          // Verify real customer still exists in database
          console.log('CustomerProvider - Verifying real customer in database...');
          const { data: verifiedCustomer, error } = await supabase
            .from('customers')
            .select('*')
            .eq('id', customerData.id)
            .maybeSingle();

          console.log('CustomerProvider - Verification result:', { verifiedCustomer: !!verifiedCustomer, error });

          if (error) {
            console.error('Customer verification error:', error);
            localStorage.removeItem('customer');
            setCustomer(null);
          } else if (verifiedCustomer) {
            console.log('CustomerProvider - Setting verified customer');
            setCustomer(verifiedCustomer);
            // Update localStorage with fresh data
            localStorage.setItem('customer', JSON.stringify(verifiedCustomer));
          } else {
            // Customer no longer exists
            console.log('CustomerProvider - Customer no longer exists, clearing');
            localStorage.removeItem('customer');
            setCustomer(null);
          }
        }
      } else {
        console.log('CustomerProvider - No stored customer found');
        setCustomer(null);
      }
    } catch (error) {
      console.error('Error refreshing customer:', error);
      localStorage.removeItem('customer');
      setCustomer(null);
    } finally {
      console.log('CustomerProvider - Finished loading, setting loading to false');
      setLoading(false);
    }
  };

  const signOut = () => {
    localStorage.removeItem('customer');
    setCustomer(null);
  };

  useEffect(() => {
    refreshCustomer();
  }, []);

  return (
    <CustomerContext.Provider value={{ 
      customer, 
      isCustomer: !!customer, 
      loading, 
      signOut,
      refreshCustomer 
    }}>
      {children}
    </CustomerContext.Provider>
  );
}

export const useCustomer = () => useContext(CustomerContext);
