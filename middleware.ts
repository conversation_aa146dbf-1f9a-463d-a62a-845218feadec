import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(req: NextRequest) {
  console.log('Middleware - Processing request for:', req.nextUrl.pathname);

  // Public routes - allow without auth check
  if (req.nextUrl.pathname === '/' ||
      req.nextUrl.pathname.startsWith('/auth/') ||
      req.nextUrl.pathname.startsWith('/admin-test')) {
    console.log('Middleware - Public route, allowing access');
    return NextResponse.next();
  }

  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req, res });

  console.log('Middleware - Getting session...');
  console.log('Middleware - Request cookies:', req.cookies.getAll().map(c => c.name));

  // Try to get session with proper error handling
  let session = null;
  try {
    const { data: { session: currentSession }, error: sessionError } = await supabase.auth.getSession();
    session = currentSession;
    console.log('Middleware - Session result:', {
      hasSession: !!session,
      userId: session?.user?.id,
      sessionError
    });
  } catch (error) {
    console.error('Middleware - Session check failed:', error);
  }

  // If no session, check for auth cookies manually
  if (!session) {
    const authCookies = req.cookies.getAll().filter(cookie =>
      cookie.name.includes('supabase') || cookie.name.includes('auth')
    );
    console.log('Middleware - Auth cookies found:', authCookies.map(c => c.name));

    // If we have auth cookies but no session, there might be a timing issue
    // For now, we'll be more permissive for admin routes
    if (authCookies.length > 0 && req.nextUrl.pathname.startsWith('/admin')) {
      console.log('Middleware - Found auth cookies, allowing admin access');
      return res;
    }
  }

  // Protected routes
  if (!session) {
    console.log('Middleware - No session, redirecting to login');
    return NextResponse.redirect(new URL('/auth/login', req.url));
  }

  // Verify admin access
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', session.user.id)
    .maybeSingle();

  console.log('Middleware - Profile check:', { profile, profileError, userId: session.user.id });

  if (profileError) {
    console.error('Middleware - Profile query error:', profileError);
    return NextResponse.redirect(new URL('/auth/login', req.url));
  }

  if (!profile || profile.role !== 'admin') {
    console.log('Middleware - Access denied:', { profile });
    return NextResponse.redirect(new URL('/', req.url));
  }

  console.log('Middleware - Admin access granted');

  return res;
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};