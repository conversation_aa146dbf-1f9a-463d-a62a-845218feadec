"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';

export default function SignUp() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const router = useRouter();
  const { toast } = useToast();

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log('Starting sign up process for:', email);
      setLoadingMessage('Creating user account...');

      // Sign up the user first
      const { data: authData, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/login`,
          data: {
            email_confirm: false // Try to skip email confirmation
          }
        }
      });

      if (signUpError) {
        console.error('Sign up error:', signUpError);
        throw signUpError;
      }
      if (!authData.user) {
        console.error('No user data returned from sign up');
        throw new Error('Failed to create user');
      }

      console.log('User created successfully:', authData.user.id);
      console.log('Auth data:', { user: !!authData.user, session: !!authData.session });

      // Check if user needs email confirmation
      if (authData.user && !authData.session) {
        console.log('User created but needs email confirmation');
        toast({
          title: "Account Created - Email Confirmation Required",
          description: "Please check your email and click the confirmation link. If you don't receive an email, check your Supabase dashboard settings to disable email confirmation for development.",
        });
        router.push('/auth/login');
        return;
      }

      setLoadingMessage('Setting up merchant account...');

      // Wait for the trigger to create the profile first
      console.log('Waiting for profile to be created by trigger...');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Now create the merchant (user should be authenticated)
      console.log('Creating merchant...');
      const { data: merchantData, error: merchantError } = await supabase
        .from('merchants')
        .insert([{ name: email.split('@')[0] }])
        .select()
        .single();

      if (merchantError) {
        console.error('Merchant creation error:', merchantError);
        // If merchant creation fails due to auth, still consider signup successful
        console.warn('Merchant creation failed, but user was created. User can create merchant after login.');
        toast({
          title: "Account Created",
          description: "Please check your email and click the confirmation link to complete your registration.",
        });
        router.push('/auth/login');
        return;
      }

      console.log('Merchant created successfully:', merchantData.id);
      setLoadingMessage('Linking merchant to profile...');

      // Update the profile with merchant_id
      console.log('Updating profile with merchant_id...');

      let profileUpdated = false;
      let retryCount = 0;
      const maxRetries = 3;

      while (!profileUpdated && retryCount < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 500)); // Brief delay

        console.log(`Attempt ${retryCount + 1}: Updating profile with merchant_id...`);
        const { error: profileError } = await supabase
          .from('profiles')
          .update({ merchant_id: merchantData.id })
          .eq('id', authData.user.id);

        if (!profileError) {
          console.log('Profile updated successfully');
          profileUpdated = true;
        } else {
          console.error(`Profile update attempt ${retryCount + 1} failed:`, profileError);
          retryCount++;

          if (retryCount >= maxRetries) {
            console.warn(`Profile update failed after ${maxRetries} attempts, but user and merchant were created successfully`);
            break;
          }
        }
      }

      toast({
        title: "Success",
        description: "Account created successfully! Please check your email to verify your account.",
      });

      router.push('/auth/login');
    } catch (error: any) {
      console.error('Sign up process failed:', error);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setLoadingMessage('');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-muted">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Create Admin Account</CardTitle>
          <CardDescription>
            Set up your merchant account and admin access
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSignUp} className="space-y-4">
            <div className="space-y-2">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={isLoading}
                minLength={6}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (loadingMessage || "Creating Account...") : "Create Admin Account"}
            </Button>
            <div className="text-center text-sm text-muted-foreground">
              Already have an account?{' '}
              <Link href="/auth/login" className="text-primary hover:underline">
                Log in
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}