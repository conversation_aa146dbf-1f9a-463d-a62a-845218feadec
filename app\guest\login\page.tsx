"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';

export default function GuestLogin() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const testDatabase = async () => {
    try {
      console.log('Testing database connection...');

      // Test customers table
      const { data: customers, error: customersError } = await supabase
        .from('customers')
        .select('*');

      console.log('All customers:', { customers, customersError });

      toast({
        title: "Database Test",
        description: `Found ${customers?.length || 0} customers. Check console for details.`,
      });
    } catch (error) {
      console.error('Database test failed:', error);
      toast({
        title: "Database Test Failed",
        description: `Error: ${error}`,
        variant: "destructive",
      });
    }
  };

  const testCustomerLookup = async () => {
    if (!email) {
      toast({
        title: "Enter Email",
        description: "Please enter an email to test",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log('Testing customer lookup for:', email);

      // Test basic connection
      const { data: allCustomers, error: allError } = await supabase
        .from('customers')
        .select('email, id')
        .limit(5);

      console.log('All customers test:', { allCustomers, allError });

      // Test specific customer lookup
      const { data: specificCustomer, error: specificError } = await supabase
        .from('customers')
        .select('*')
        .eq('email', email)
        .maybeSingle();

      console.log('Specific customer lookup:', { specificCustomer, specificError });

      toast({
        title: "Customer Lookup Test",
        description: `Found ${allCustomers?.length || 0} total customers. Specific customer: ${specificCustomer ? 'Found' : 'Not found'}`,
      });
    } catch (error) {
      console.error('Customer lookup test failed:', error);
      toast({
        title: "Test Failed",
        description: `Error: ${error}`,
        variant: "destructive",
      });
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log('Starting guest login process for:', email);

      console.log('Step 1: Looking up guest user...');
      // Find guest user by email in profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select(`
          id,
          role,
          merchant_id,
          customers!inner(email, full_name)
        `)
        .eq('role', 'guest')
        .eq('customers.email', email)
        .maybeSingle();

      console.log('Profile lookup result:', { profileData: !!profileData, profileError });

      if (profileError) {
        console.error('Profile lookup error:', profileError);
        throw new Error('Failed to find account. Please try again.');
      }

      if (!profileData) {
        throw new Error('No guest account found with this email. Please sign up first.');
      }

      console.log('Step 2: Guest profile found:', profileData.id);

      // Get the customer data
      const customerData = (profileData.customers as any);

      console.log('Step 3: Signing in with Supabase auth...');
      // For guest login, we'll use a simplified approach
      // In a real app, you might want to implement passwordless login or magic links

      // For now, create a session-like object for the guest
      const guestSession = {
        id: profileData.id,
        email: customerData.email,
        full_name: customerData.full_name,
        role: profileData.role,
        merchant_id: profileData.merchant_id,
        joined_at: new Date().toISOString()
      };

      // Store guest session info
      localStorage.setItem('customer', JSON.stringify(guestSession));

      toast({
        title: "Welcome Back!",
        description: `Hello ${customerData.full_name || 'there'}! You're now signed in.`,
      });

      console.log('Step 4: Redirecting to guest dashboard...');
      router.push('/guest');
    } catch (error: any) {
      console.error('Guest login process failed:', error);
      toast({
        title: "Login Error",
        description: error.message || 'An unexpected error occurred',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-muted">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Welcome Back</CardTitle>
          <CardDescription>
            Sign in to access your loyalty account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Signing in..." : "Sign In"}
            </Button>
            <Button type="button" variant="outline" className="w-full" onClick={testDatabase}>
              Test Database
            </Button>
            <div className="text-center text-sm text-muted-foreground">
              Don't have an account?{' '}
              <Link href="/guest/signup" className="text-primary hover:underline">
                Join now
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
