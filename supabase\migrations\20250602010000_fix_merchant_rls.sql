-- Fix RLS policies for merchants table to allow authenticated users to create merchants

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow authenticated users to create merchants" ON merchants;
DROP POLICY IF EXISTS "Allow users to view merchants" ON merchants;
DROP POLICY IF EXISTS "Allow users to update their own merchant" ON merchants;

-- Create more permissive policies for merchants
CREATE POLICY "Allow authenticated users to create merchants" ON merchants
    FOR INSERT TO authenticated
    WITH CHECK (true);

CREATE POLICY "Allow authenticated users to view merchants" ON merchants
    FOR SELECT TO authenticated
    USING (true);

CREATE POLICY "Allow authenticated users to update merchants" ON merchants
    FOR UPDATE TO authenticated
    USING (true);

-- Also ensure customers table has proper policies
DROP POLICY IF EXISTS "Allow public to create customers" ON customers;
DROP POLICY IF EXISTS "Allow public to view customers" ON customers;
DROP POLICY IF EXISTS "Allow public to update customers" ON customers;

-- Create policies for customers (allow public access for guest signup)
CREATE POLICY "Allow public to create customers" ON customers
    FOR INSERT TO public
    WITH CHECK (true);

CREATE POLICY "Allow public to view customers" ON customers
    FOR SELECT TO public
    USING (true);

CREATE POLICY "Allow public to update customers" ON customers
    FOR UPDATE TO public
    USING (true);

-- Ensure RLS is enabled
ALTER TABLE merchants ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
