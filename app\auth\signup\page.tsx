"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';

export default function SignUp() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const router = useRouter();
  const { toast } = useToast();

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log('Starting sign up process for:', email);
      setLoadingMessage('Creating merchant account...');

      // Create the merchant first
      console.log('Creating merchant...');
      const { data: merchantData, error: merchantError } = await supabase
        .from('merchants')
        .insert([{ name: email.split('@')[0] }])
        .select()
        .single();

      if (merchantError) {
        console.error('Merchant creation error:', merchantError);
        throw merchantError;
      }

      console.log('Merchant created successfully:', merchantData.id);
      setLoadingMessage('Creating user account...');

      // Sign up the user with metadata
      const { data: authData, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            merchant_id: merchantData.id
          }
        }
      });

      if (signUpError) {
        console.error('Sign up error:', signUpError);
        throw signUpError;
      }
      if (!authData.user) {
        console.error('No user data returned from sign up');
        throw new Error('Failed to create user');
      }

      console.log('User created successfully:', authData.user.id);
      setLoadingMessage('Setting up profile...');

      // Wait for the trigger to create the profile, then update it
      // The database trigger automatically creates a profile when a user signs up
      console.log('Setting up profile with merchant association...');

      let profileUpdated = false;
      let retryCount = 0;
      const maxRetries = 6;
      const startTime = Date.now();
      const maxWaitTime = 10000; // 10 seconds maximum

      while (!profileUpdated && retryCount < maxRetries) {
        // Check if we've exceeded the maximum wait time
        if (Date.now() - startTime > maxWaitTime) {
          console.warn('Profile update timed out, but user was created successfully');
          break;
        }

        await new Promise(resolve => setTimeout(resolve, 500 + (retryCount * 300))); // Progressive delay

        console.log(`Attempt ${retryCount + 1}: Updating profile with merchant_id...`);
        const { error: profileError } = await supabase
          .from('profiles')
          .update({ merchant_id: merchantData.id })
          .eq('id', authData.user.id);

        if (!profileError) {
          console.log('Profile updated successfully');
          profileUpdated = true;
        } else {
          console.error(`Profile update attempt ${retryCount + 1} failed:`, profileError);
          retryCount++;

          if (retryCount >= maxRetries) {
            // Don't fail the entire signup if profile update fails
            // The user can still login and we can fix this later
            console.warn(`Profile update failed after ${maxRetries} attempts, but user was created successfully`);
            break;
          }
        }
      }

      toast({
        title: "Success",
        description: "Account created successfully! Please check your email to verify your account.",
      });

      router.push('/auth/login');
    } catch (error: any) {
      console.error('Sign up process failed:', error);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-muted">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Create Admin Account</CardTitle>
          <CardDescription>
            Set up your merchant account and admin access
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSignUp} className="space-y-4">
            <div className="space-y-2">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={isLoading}
                minLength={6}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (loadingMessage || "Creating Account...") : "Create Admin Account"}
            </Button>
            <div className="text-center text-sm text-muted-foreground">
              Already have an account?{' '}
              <Link href="/auth/login" className="text-primary hover:underline">
                Log in
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}