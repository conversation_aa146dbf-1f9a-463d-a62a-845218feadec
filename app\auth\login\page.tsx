"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log('Starting login process for:', email);

      // Add timeout to prevent hanging
      const loginPromise = supabase.auth.signInWithPassword({
        email,
        password,
      });

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Login request timed out after 15 seconds')), 15000);
      });

      const { data, error } = await Promise.race([loginPromise, timeoutPromise]) as any;

      console.log('Login result:', { data, error });

      if (error) {
        console.error('Login error:', error);
        throw error;
      }

      if (data.user) {
        console.log('User logged in successfully:', data.user.id);
        console.log('Checking user profile...');

        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('role, merchant_id')
          .eq('id', data.user.id)
          .maybeSingle();

        console.log('Profile query result:', { profile, profileError });

        if (profileError) {
          console.error('Profile query error:', profileError);
          throw new Error('Failed to load user profile');
        }

        if (!profile) {
          console.error('No profile found for user');
          throw new Error('User profile not found. Please contact support.');
        }

        console.log('User role:', profile.role);
        if (profile.role === 'admin') {
          console.log('Admin access granted, waiting for auth state to update...');

          // Wait a bit for the auth state to propagate
          await new Promise(resolve => setTimeout(resolve, 500));

          console.log('Redirecting to admin dashboard');
          router.replace('/admin');
          return;
        }
        throw new Error('Unauthorized access - admin role required');
      } else {
        throw new Error('Login failed - no user data received');
      }
    } catch (error: any) {
      console.error('Login process failed:', error);
      toast({
        title: "Login Error",
        description: error.message || 'An unexpected error occurred',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-muted">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Admin Login</CardTitle>
          <CardDescription>
            Sign in to manage your loyalty programs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Logging in..." : "Login"}
            </Button>
            <div className="text-center text-sm text-muted-foreground">
              Need an admin account?{' '}
              <Link href="/auth/signup" className="text-primary hover:underline">
                Sign up
              </Link>
            </div>
            <div className="text-center text-sm text-muted-foreground">
              <Link href="/admin" className="text-primary hover:underline">
                Direct Admin Link (for testing)
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}