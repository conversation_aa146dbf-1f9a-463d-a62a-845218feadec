"use client";

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Gift, Star, Clock, Trophy } from "lucide-react";
import { useCustomer } from '@/components/auth/CustomerProvider';
import { supabase } from '@/lib/supabase';

interface CustomerPoints {
  program_id: string;
  balance: number;
  program_name: string;
}

interface Reward {
  id: string;
  name: string;
  cost_points: number;
  stock: number;
  program_name: string;
}

export default function GuestDashboard() {
  const { customer } = useCustomer();
  const [points, setPoints] = useState<CustomerPoints[]>([]);
  const [rewards, setRewards] = useState<Reward[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (customer) {
      loadCustomerData();
    }
  }, [customer]);

  const loadCustomerData = async () => {
    if (!customer) return;

    try {
      // Load customer points from the view
      const { data: pointsData, error: pointsError } = await supabase
        .from('v_customer_balances')
        .select(`
          customer_id,
          program_id,
          balance,
          loyalty_programs!inner(name)
        `)
        .eq('customer_id', customer.id);

      if (pointsError) {
        console.error('Error loading points:', pointsError);
      } else {
        const formattedPoints = pointsData?.map(p => ({
          program_id: p.program_id,
          balance: p.balance || 0,
          program_name: (p.loyalty_programs as any)?.name || 'Unknown Program'
        })) || [];
        setPoints(formattedPoints);
      }

      // Load available rewards for this merchant
      const { data: rewardsData, error: rewardsError } = await supabase
        .from('rewards')
        .select(`
          id,
          name,
          cost_points,
          stock,
          loyalty_programs!inner(name, merchant_id)
        `)
        .eq('loyalty_programs.merchant_id', customer.merchant_id)
        .gt('stock', 0);

      if (rewardsError) {
        console.error('Error loading rewards:', rewardsError);
      } else {
        const formattedRewards = rewardsData?.map(r => ({
          id: r.id,
          name: r.name,
          cost_points: r.cost_points,
          stock: r.stock,
          program_name: (r.loyalty_programs as any)?.name || 'Unknown Program'
        })) || [];
        setRewards(formattedRewards);
      }
    } catch (error) {
      console.error('Error loading customer data:', error);
    } finally {
      setLoading(false);
    }
  };

  const totalPoints = points.reduce((sum, p) => sum + p.balance, 0);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div>Loading your loyalty data...</div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-8 text-center">
        <h2 className="text-3xl font-bold mb-4">
          Welcome back, {customer?.full_name || 'Valued Customer'}!
        </h2>
        <p className="text-muted-foreground">
          Keep earning points and redeem exciting rewards
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="text-center">
            <Star className="w-12 h-12 mx-auto mb-2 text-primary" />
            <CardTitle>Total Points</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-4xl font-bold">{totalPoints}</p>
            <p className="text-muted-foreground">Points Available</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <Gift className="w-12 h-12 mx-auto mb-2 text-primary" />
            <CardTitle>Available Rewards</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-4xl font-bold">{rewards.length}</p>
            <p className="text-muted-foreground">Rewards to Claim</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <Trophy className="w-12 h-12 mx-auto mb-2 text-primary" />
            <CardTitle>Member Since</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-lg font-bold">
              {customer ? new Date(customer.joined_at).toLocaleDateString() : 'N/A'}
            </p>
            <p className="text-muted-foreground">Join Date</p>
          </CardContent>
        </Card>
      </div>

      {points.length > 0 && (
        <div className="mb-8">
          <h3 className="text-xl font-bold mb-4">Your Points by Program</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {points.map((point) => (
              <Card key={point.program_id}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-semibold">{point.program_name}</p>
                      <p className="text-sm text-muted-foreground">Loyalty Program</p>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold">{point.balance}</p>
                      <p className="text-sm text-muted-foreground">points</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      <div>
        <h3 className="text-xl font-bold mb-4">Available Rewards</h3>
        {rewards.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {rewards.map((reward) => (
              <Card key={reward.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-semibold">{reward.name}</h4>
                    <span className="text-sm bg-primary/10 text-primary px-2 py-1 rounded">
                      {reward.cost_points} pts
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">{reward.program_name}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      {reward.stock} available
                    </span>
                    <span className={`text-sm ${totalPoints >= reward.cost_points ? 'text-green-600' : 'text-red-600'}`}>
                      {totalPoints >= reward.cost_points ? 'Can redeem' : 'Need more points'}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center p-8">
              <Clock className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">
                No rewards available yet. Check back soon!
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}