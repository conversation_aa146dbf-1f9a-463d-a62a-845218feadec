create extension if not exists "pgcrypto"; -- gen_random_uuid()

create table public.merchants (
  id          uuid default gen_random_uuid() primary key,
  name        text not null,
  created_at  timestamptz default now()
);

create table public.profiles (
  id          uuid  primary key references auth.users(id) on delete cascade,
  merchant_id uuid  references public.merchants(id),
  role        text  not null check (role in ('admin','staff')),
  created_at  timestamptz default now()
);

create or replace function public.handle_new_user()
returns trigger language plpgsql security definer set search_path = public as $$
begin
  insert into public.profiles (id, role)
  values (new.id, 'admin');
  return new;
end; $$;

create trigger on_auth_user_created
after insert on auth.users
for each row execute procedure public.handle_new_user();

create table public.customers (
  id          uuid default gen_random_uuid() primary key,
  merchant_id uuid not null references public.merchants(id),
  email       text,
  full_name   text,
  joined_at   timestamptz default now(),
  unique(email, merchant_id)      -- same person can join other shops
);

create table public.loyalty_programs (
  id          uuid default gen_random_uuid() primary key,
  merchant_id uuid not null references public.merchants(id),
  name        text not null,
  type        text not null  -- 'points' | 'punch' | ... (enum later)
);

create table public.loyalty_enrollments (
  program_id  uuid references public.loyalty_programs(id) on delete cascade,
  customer_id uuid references public.customers(id)        on delete cascade,
  enrolled_at timestamptz default now(),
  primary key (program_id, customer_id)
);

create table public.rewards (
  id          uuid default gen_random_uuid() primary key,
  program_id  uuid not null references public.loyalty_programs(id),
  name        text not null,
  cost_points int  not null check (cost_points > 0),
  stock       int  default 0,
  expires_at  timestamptz
);

create table public.points_txns (
  id            uuid default gen_random_uuid() primary key,
  program_id    uuid not null references public.loyalty_programs(id),
  customer_id   uuid not null references public.customers(id),
  delta_points  int  not null,              -- +earn / –spend
  source        text not null,              -- 'purchase', 'manual', ...
  ref           text,                       -- order ID, etc.
  created_at    timestamptz default now()
);

create table public.redemptions (
  id            uuid default gen_random_uuid() primary key,
  reward_id     uuid references public.rewards(id),
  customer_id   uuid references public.customers(id),
  program_id    uuid,
  points_spent  int  not null,
  status        text default 'pending',     -- 'pending'|'fulfilled'|'canceled'
  redeemed_at   timestamptz default now()
);


create policy "Self profile"
on public.profiles
for all
to authenticated
using ( id = auth.uid() );  -- Supabase helper function :contentReference[oaicite:0]{index=0}


create policy "Merchant admins manage everything"
on public.loyalty_programs
for all
to authenticated
using (
  exists (
    select 1
    from public.profiles p
    where p.id = auth.uid()
      and p.role in ('admin','staff')
      and p.merchant_id = merchant_id
  )
);

create view public.v_customer_balances as
select customer_id,
       program_id,
       sum(delta_points) as balance
from public.points_txns
group by 1,2;