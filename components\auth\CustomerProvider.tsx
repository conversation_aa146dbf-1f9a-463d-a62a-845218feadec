"use client";

import { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';

interface Customer {
  id: string;
  email: string;
  full_name: string;
  merchant_id: string;
  joined_at: string;
}

interface CustomerContextType {
  customer: Customer | null;
  isCustomer: boolean;
  loading: boolean;
  signOut: () => void;
  refreshCustomer: () => Promise<void>;
}

const CustomerContext = createContext<CustomerContextType>({
  customer: null,
  isCustomer: false,
  loading: true,
  signOut: () => {},
  refreshCustomer: async () => {},
});

export function CustomerProvider({ children }: { children: React.ReactNode }) {
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);

  const refreshCustomer = async () => {
    try {
      const storedCustomer = localStorage.getItem('customer');
      if (storedCustomer) {
        const customerData = JSON.parse(storedCustomer);
        
        // Verify customer still exists in database
        const { data: verifiedCustomer, error } = await supabase
          .from('customers')
          .select('*')
          .eq('id', customerData.id)
          .maybeSingle();

        if (error) {
          console.error('Customer verification error:', error);
          localStorage.removeItem('customer');
          setCustomer(null);
        } else if (verifiedCustomer) {
          setCustomer(verifiedCustomer);
          // Update localStorage with fresh data
          localStorage.setItem('customer', JSON.stringify(verifiedCustomer));
        } else {
          // Customer no longer exists
          localStorage.removeItem('customer');
          setCustomer(null);
        }
      } else {
        setCustomer(null);
      }
    } catch (error) {
      console.error('Error refreshing customer:', error);
      localStorage.removeItem('customer');
      setCustomer(null);
    } finally {
      setLoading(false);
    }
  };

  const signOut = () => {
    localStorage.removeItem('customer');
    setCustomer(null);
  };

  useEffect(() => {
    refreshCustomer();
  }, []);

  return (
    <CustomerContext.Provider value={{ 
      customer, 
      isCustomer: !!customer, 
      loading, 
      signOut,
      refreshCustomer 
    }}>
      {children}
    </CustomerContext.Provider>
  );
}

export const useCustomer = () => useContext(CustomerContext);
