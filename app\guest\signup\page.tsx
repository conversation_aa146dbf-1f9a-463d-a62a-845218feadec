"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';

export default function GuestSignUp() {
  const [email, setEmail] = useState('');
  const [fullName, setFullName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const router = useRouter();
  const { toast } = useToast();

  const testDatabaseConnection = async () => {
    try {
      console.log('Testing database connection...');

      // Test basic connection
      const { data: testData, error: testError } = await supabase
        .from('merchants')
        .select('count')
        .limit(1);

      console.log('Basic connection test:', { testData, testError });

      // Test merchant query
      const { data: merchants, error: merchantError } = await supabase
        .from('merchants')
        .select('*');

      console.log('All merchants:', { merchants, merchantError });

      toast({
        title: "Database Test",
        description: `Found ${merchants?.length || 0} merchants. Check console for details.`,
      });
    } catch (error) {
      console.error('Database test failed:', error);
      toast({
        title: "Database Test Failed",
        description: `Error: ${error}`,
        variant: "destructive",
      });
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log('Starting guest signup process for:', email);
      setLoadingMessage('Creating your account...');

      console.log('Step 1: Looking for merchant...');
      // First, try to find an existing merchant
      const { data: existingMerchants, error: lookupError } = await supabase
        .from('merchants')
        .select('id, name')
        .limit(1);

      console.log('Existing merchants:', { existingMerchants, lookupError });

      let merchantId;

      if (lookupError) {
        console.error('Merchant lookup failed:', lookupError);
        throw new Error('Database error. Please try again.');
      }

      if (existingMerchants && existingMerchants.length > 0) {
        // Use existing merchant
        merchantId = existingMerchants[0].id;
        console.log('Step 2: Using existing merchant:', merchantId);
      } else {
        // No merchants exist, create a default one
        console.log('Step 2: No merchants found, creating default merchant...');
        const { data: newMerchant, error: createError } = await supabase
          .from('merchants')
          .insert([{ name: 'Default Business' }])
          .select('id')
          .single();

        console.log('Merchant creation result:', { newMerchant, createError });

        if (createError || !newMerchant) {
          console.error('Failed to create merchant:', createError);
          throw new Error('Failed to set up business account. Please contact support.');
        }

        merchantId = newMerchant.id;
        console.log('Step 2: Created new merchant:', merchantId);
      }

      console.log('Step 3: Creating customer account...');
      // Create customer account
      const customerPromise = supabase
        .from('customers')
        .insert([{
          email,
          full_name: fullName,
          merchant_id: merchantId
        }])
        .select()
        .single();

      const customerTimeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Customer creation timed out after 10 seconds')), 10000);
      });

      const { data: customerData, error: customerError } = await Promise.race([customerPromise, customerTimeoutPromise]) as any;

      console.log('Customer creation result:', { customerData, customerError });

      if (customerError) {
        console.error('Customer creation error:', customerError);
        if (customerError.code === '23505') { // Unique constraint violation
          throw new Error('An account with this email already exists.');
        }
        throw new Error(`Failed to create account: ${customerError.message || 'Unknown error'}`);
      }

      console.log('Step 4: Customer created successfully:', customerData.id);

      // Store customer info in localStorage for simple auth
      localStorage.setItem('customer', JSON.stringify(customerData));

      toast({
        title: "Account Created!",
        description: "Welcome to our loyalty program! You can now start earning points.",
      });

      router.push('/guest');
    } catch (error: any) {
      console.error('Guest signup process failed:', error);
      toast({
        title: "Signup Error",
        description: error.message || 'An unexpected error occurred',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setLoadingMessage('');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-muted">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Join Our Loyalty Program</CardTitle>
          <CardDescription>
            Sign up to start earning points and redeeming rewards
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSignUp} className="space-y-4">
            <div className="space-y-2">
              <Input
                type="text"
                placeholder="Full Name"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (loadingMessage || "Creating Account...") : "Join Loyalty Program"}
            </Button>
            <Button type="button" variant="outline" className="w-full" onClick={testDatabaseConnection}>
              Test Database Connection
            </Button>
            <div className="text-center text-sm text-muted-foreground">
              Already have an account?{' '}
              <Link href="/guest/login" className="text-primary hover:underline">
                Sign in
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
