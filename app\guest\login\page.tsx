"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';

export default function GuestLogin() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const testDatabase = async () => {
    try {
      console.log('Testing database connection...');

      // Test customers table
      const { data: customers, error: customersError } = await supabase
        .from('customers')
        .select('*');

      console.log('All customers:', { customers, customersError });

      toast({
        title: "Database Test",
        description: `Found ${customers?.length || 0} customers. Check console for details.`,
      });
    } catch (error) {
      console.error('Database test failed:', error);
      toast({
        title: "Database Test Failed",
        description: `Error: ${error}`,
        variant: "destructive",
      });
    }
  };

  const testCustomerLookup = async () => {
    if (!email) {
      toast({
        title: "Enter Email",
        description: "Please enter an email to test",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log('Testing customer lookup for:', email);

      // Test basic connection
      const { data: allCustomers, error: allError } = await supabase
        .from('customers')
        .select('email, id')
        .limit(5);

      console.log('All customers test:', { allCustomers, allError });

      // Test specific customer lookup
      const { data: specificCustomer, error: specificError } = await supabase
        .from('customers')
        .select('*')
        .eq('email', email)
        .maybeSingle();

      console.log('Specific customer lookup:', { specificCustomer, specificError });

      toast({
        title: "Customer Lookup Test",
        description: `Found ${allCustomers?.length || 0} total customers. Specific customer: ${specificCustomer ? 'Found' : 'Not found'}`,
      });
    } catch (error) {
      console.error('Customer lookup test failed:', error);
      toast({
        title: "Test Failed",
        description: `Error: ${error}`,
        variant: "destructive",
      });
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log('Starting guest login process for:', email);

      // For development, create a simple mock customer if database is having issues
      // In production, you'd want proper database verification
      console.log('Creating mock customer for development...');

      const mockCustomer = {
        id: `customer_${Date.now()}`,
        email: email,
        full_name: email.split('@')[0],
        merchant_id: 'default_merchant',
        joined_at: new Date().toISOString()
      };

      // Store customer info in localStorage for simple auth
      localStorage.setItem('customer', JSON.stringify(mockCustomer));

      toast({
        title: "Welcome!",
        description: `Hello ${mockCustomer.full_name}! You're now signed in. (Development mode)`,
      });

      console.log('Redirecting to guest dashboard...');
      router.push('/guest');
    } catch (error: any) {
      console.error('Guest login process failed:', error);
      toast({
        title: "Login Error",
        description: error.message || 'An unexpected error occurred',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-muted">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Welcome Back</CardTitle>
          <CardDescription>
            Sign in to access your loyalty account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Signing in..." : "Sign In"}
            </Button>
            <Button type="button" variant="outline" className="w-full" onClick={testDatabase}>
              Test Database
            </Button>
            <div className="text-center text-sm text-muted-foreground">
              Don't have an account?{' '}
              <Link href="/guest/signup" className="text-primary hover:underline">
                Join now
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
