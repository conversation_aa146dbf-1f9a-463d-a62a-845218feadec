"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function CreateProgram() {
  return (
    <div className="container mx-auto px-4 py-8">
      <Link href="/" className="inline-flex items-center mb-6 text-muted-foreground hover:text-primary">
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Dashboard
      </Link>

      <Card>
        <CardHeader>
          <CardTitle>Create New Loyalty Program</CardTitle>
          <CardDescription>
            Set up a new rewards program for your business
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Program Name</Label>
              <Input
                id="name"
                placeholder="e.g., Coffee Lovers Rewards"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Program Type</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select program type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="points">Points Based</SelectItem>
                  <SelectItem value="visits">Visit Based</SelectItem>
                  <SelectItem value="spend">Spend Based</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Program Description</Label>
              <Textarea
                id="description"
                placeholder="Describe your loyalty program..."
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="pointsPerDollar">Points per Dollar</Label>
                <Input
                  id="pointsPerDollar"
                  type="number"
                  min="0"
                  step="0.1"
                  placeholder="1"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="minimumPoints">Minimum Points for Reward</Label>
                <Input
                  id="minimumPoints"
                  type="number"
                  min="0"
                  placeholder="100"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Program Benefits</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="birthdayReward" className="rounded" />
                  <Label htmlFor="birthdayReward">Birthday Rewards</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="referralBonus" className="rounded" />
                  <Label htmlFor="referralBonus">Referral Bonus</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="welcomeBonus" className="rounded" />
                  <Label htmlFor="welcomeBonus">Welcome Bonus</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="doublePoints" className="rounded" />
                  <Label htmlFor="doublePoints">Double Points Days</Label>
                </div>
              </div>
            </div>

            <Button type="submit" className="w-full">
              Create Program
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}