"use client";

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { CustomerProvider, useCustomer } from '@/components/auth/CustomerProvider';
import { Button } from '@/components/ui/button';
import { LogOut, User } from 'lucide-react';

function GuestLayoutContent({ children }: { children: React.ReactNode }) {
  const { customer, isCustomer, loading, signOut } = useCustomer();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!loading && !isCustomer && pathname === '/guest') {
      router.push('/guest/login');
    }
  }, [isCustomer, loading, router, pathname]);

  const handleSignOut = () => {
    signOut();
    router.push('/guest/login');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold">Loyalty Rewards</h1>
          {isCustomer && (
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <User className="w-4 h-4" />
                <span>{customer?.full_name || customer?.email}</span>
              </div>
              <Button
                variant="ghost"
                onClick={handleSignOut}
                className="flex items-center gap-2"
              >
                <LogOut className="w-4 h-4" />
                Sign Out
              </Button>
            </div>
          )}
        </div>
      </header>
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
    </div>
  );
}

export default function GuestLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <CustomerProvider>
      <GuestLayoutContent>{children}</GuestLayoutContent>
    </CustomerProvider>
  );
}